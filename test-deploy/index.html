<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TSConv Test Deploy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            text-align: center;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .status {
            background: rgba(0, 255, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 TSConv Cloudflare 部署测试</h1>
        
        <div class="status">
            <h2>✅ 部署状态：成功</h2>
            <p>Cloudflare Pages 部署正常工作！</p>
        </div>

        <div class="info">
            <h3>📋 部署信息</h3>
            <ul>
                <li><strong>部署时间：</strong> <span id="deployTime"></span></li>
                <li><strong>项目：</strong> TSConv - Timestamp Converter</li>
                <li><strong>平台：</strong> Cloudflare Pages</li>
                <li><strong>构建工具：</strong> Vite + TypeScript</li>
                <li><strong>框架：</strong> React + Tailwind CSS</li>
            </ul>
        </div>

        <div class="info">
            <h3>🔗 相关链接</h3>
            <ul>
                <li><a href="https://tsconv.com" style="color: #87CEEB;">主站点 - tsconv.com</a></li>
                <li><a href="https://api.tsconv.com" style="color: #87CEEB;">API 端点 - api.tsconv.com</a></li>
                <li><a href="https://tsconv.pages.dev" style="color: #87CEEB;">Pages 默认域名</a></li>
            </ul>
        </div>

        <div class="info">
            <h3>🚀 下一步</h3>
            <p>部署测试成功！现在可以：</p>
            <ul>
                <li>验证所有功能正常工作</li>
                <li>测试 API 端点</li>
                <li>检查性能指标</li>
                <li>配置自定义域名</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('deployTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 简单的功能测试
        console.log('🎯 TSConv Cloudflare 部署测试页面加载成功');
        console.log('📅 部署时间:', new Date().toISOString());
        console.log('🌐 用户代理:', navigator.userAgent);
        console.log('📍 页面 URL:', window.location.href);
    </script>
</body>
</html>
